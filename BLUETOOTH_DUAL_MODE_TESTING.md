# 蓝牙双向通信测试指南

## 功能概述

现在应用支持完整的蓝牙双向通信功能：
- **服务器模式（接收方）**: 监听来自其他设备的连接和数据
- **客户端模式（发送方）**: 扫描、配对设备并发送数据

## 测试准备

### 1. 安装应用
在两台Android设备上安装相同的APK文件

### 2. 权限确认
确保两台设备都已授予蓝牙相关权限：
- BLUETOOTH_SCAN
- BLUETOOTH_CONNECT  
- ACCESS_FINE_LOCATION

## 测试步骤

### 第一步：设备A启动服务器
1. 打开应用
2. 在"蓝牙服务器（接收方）"部分点击"启动服务器"
3. 看到绿色指示灯和"服务器运行中，等待连接..."提示
4. 记录设备A的蓝牙名称和地址

### 第二步：设备B扫描并配对
1. 打开应用
2. 在"蓝牙客户端（发送方）"部分点击"开始扫描"
3. 在"已发现设备"列表中找到设备A
4. 点击设备A的"配对"按钮
5. 在设备A上确认配对请求
6. 配对成功后，设备A会出现在"已配对设备"列表中

### 第三步：发送数据测试
1. 在设备B的"已配对设备"列表中找到设备A
2. 点击"发送数据"按钮
3. 在弹出的对话框中：
   - 手动输入文本，或
   - 点击预设按钮（Hello、测试、LED_ON、LED_OFF）
4. 点击"发送"

### 第四步：验证接收
1. 在设备A上查看"蓝牙服务器（接收方）"部分
2. 应该能看到：
   - 连接提示："客户端已连接: [设备B名称]"
   - 接收到的消息显示在消息列表中
   - 消息包含发送方地址、内容和时间戳

## 预期结果

### 成功的通信流程：
```
设备A: 启动服务器 → 等待连接
设备B: 扫描设备 → 配对设备A → 发送数据
设备A: 接受连接 → 接收数据 → 显示消息 → 发送确认响应
设备B: 收到响应确认
```

### 界面显示：
- **设备A（服务器）**: 消息列表显示接收到的数据
- **设备B（客户端）**: 错误信息显示发送成功和响应内容

## 测试用例

### 1. 基本文本发送
- 发送: "Hello World"
- 预期: 设备A收到并显示该消息

### 2. 中文文本发送  
- 发送: "你好世界"
- 预期: 正确显示中文字符

### 3. 控制命令发送
- 发送: "LED_ON", "LED_OFF"
- 预期: 命令正确传输

### 4. 长文本发送
- 发送: 超过100字符的长文本
- 预期: 完整接收所有内容

### 5. 快速连续发送
- 连续发送多条消息
- 预期: 所有消息都能正确接收和显示

## 故障排除

### 1. 配对失败
- 确保两台设备蓝牙都已开启
- 检查权限是否完全授予
- 尝试清除蓝牙缓存后重试

### 2. 连接失败
- 确保服务器已启动
- 检查设备是否在蓝牙范围内
- 重启蓝牙后重试

### 3. 数据发送失败
- 确保设备已正确配对
- 检查服务器是否仍在运行
- 查看错误信息获取详细原因

### 4. 消息接收不完整
- 检查网络环境是否稳定
- 尝试发送较短的测试消息
- 重启应用后重试

## 技术细节

### 服务器端实现
- 使用`BluetoothServerSocket.listenUsingRfcommWithServiceRecord()`
- 标准SPP UUID: `00001101-0000-1000-8000-00805F9B34FB`
- 多线程处理多个客户端连接
- 自动发送确认响应

### 客户端端实现
- 临时Socket连接进行数据传输
- 支持多种连接方法（标准SPP + 反射方法）
- 发送后自动关闭连接
- 显示详细的发送过程和结果

### 数据格式
- 使用UTF-8编码
- 支持任意长度文本
- 自动处理中文和特殊字符

这个测试方案可以完整验证蓝牙双向通信功能，确保数据能够在两台设备之间正确传输。
