# 更新日志

## [1.1.1] - 2025-08-01

### 修复问题
- **修复SDK初始化崩溃**: 解决了 `Lazy.getValue() on a null object reference` 错误
- **BLE管理器延迟初始化**: BLE管理器现在只在第一次使用BLE功能时才初始化，避免初始化时的空引用问题
- **改进资源管理**: 优化了BLE资源的清理逻辑，只有在BLE管理器已初始化时才进行清理

### 技术改进
- 将BLE管理器从lazy初始化改为手动延迟初始化
- 添加了`getBleManager()`私有方法来管理BLE管理器的生命周期
- 改进了错误处理和资源清理机制

## [1.1.0] - 2025-08-01

### 新增功能
- **BLE (蓝牙低功耗) 支持**: 添加了完整的BLE功能支持
  - BLE设备扫描和发现
  - BLE设备连接和断开
  - GATT服务发现
  - 特征值订阅和通知接收
  - 主动订阅0xFFE4特征值的Notify通知

### 新增API
- `BluetoothManager.startBleScan()` - 开始BLE扫描
- `BluetoothManager.stopBleScan()` - 停止BLE扫描
- `BluetoothManager.connectToBleDevice(deviceAddress)` - 连接BLE设备
- `BluetoothManager.subscribeToFFE4Notification(deviceAddress)` - 订阅0xFFE4特征值通知
- `BluetoothManager.subscribeToCharacteristic(deviceAddress, uuid)` - 订阅指定特征值通知
- `BluetoothManager.disconnectBleDevice(deviceAddress)` - 断开BLE设备
- `BluetoothManager.getConnectedBleDevices()` - 获取已连接的BLE设备列表

### 新增回调
- `BluetoothCallback.onCharacteristicNotification()` - BLE特征值通知回调
- `BluetoothCallback.onBleConnectionStateChanged()` - BLE连接状态变化回调

### 新增组件
- `BleManager` - BLE功能管理器
- `BleControlSection` - Demo应用中的BLE控制界面

### 改进
- 在Demo应用中添加了BLE功能演示界面
- 更新了权限处理以支持BLE相关权限
- 改进了资源清理机制，包含BLE资源的清理

### 文档
- 添加了 `BLE_USAGE_GUIDE.md` - BLE功能使用指南
- 更新了README文档以包含BLE功能说明

## [1.0.0] - 2025-07-01

### 初始版本
- 经典蓝牙设备扫描和发现
- 蓝牙设备连接和配对
- 数据发送和接收
- 蓝牙服务器功能
- 连接池管理
- 权限处理
- Demo应用

### 主要功能
- 蓝牙设备搜索
- 设备连接和配对
- 数据传输
- 连接状态管理
- 错误处理和重试机制
