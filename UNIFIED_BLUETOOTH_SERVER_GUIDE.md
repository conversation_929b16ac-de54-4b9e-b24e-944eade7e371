# 统一蓝牙服务器使用指南

## 🎯 功能概述

统一蓝牙服务器是一个强大的功能，可以**同时接收**来自两种不同蓝牙协议的消息：
- **经典蓝牙设备**：通过SPP协议发送的消息
- **BLE设备**：通过特征值通知发送的消息（如0xFFE4按键信号）

## 🚀 快速开始

### 1. 启动统一蓝牙服务器

在Demo应用中：
1. 找到"蓝牙服务器"部分
2. 点击**"启动统一蓝牙服务器 (推荐)"**按钮
3. 看到绿色状态指示器表示服务器运行中

### 2. 连接BLE设备（如蓝牙按键）

在"BLE功能"部分：
1. 输入蓝牙按键的MAC地址（格式：AA:BB:CC:DD:EE:FF）
2. 点击**"快速连接并订阅 (一键完成)"**按钮
3. 等待连接成功和自动订阅完成

### 3. 测试接收消息

- **测试BLE按键**：按下蓝牙按键，消息会出现在服务器的消息列表中
- **测试经典蓝牙**：使用支持SPP协议的蓝牙设备发送消息

## 📋 详细操作步骤

### 步骤1：准备工作
```
1. 确保手机蓝牙已开启
2. 确保应用已获得蓝牙权限
3. 获取要连接的BLE设备MAC地址
```

### 步骤2：启动统一服务器
```
1. 打开Demo应用
2. 滚动到"蓝牙服务器"部分
3. 点击"启动统一蓝牙服务器 (推荐)"
4. 确认看到"统一服务器运行中"状态
```

### 步骤3：连接BLE设备
```
1. 滚动到"BLE功能"部分
2. 在"BLE设备地址"框中输入MAC地址
3. 点击"快速连接并订阅"按钮
4. 等待连接成功提示
```

### 步骤4：验证功能
```
1. 按下蓝牙按键
2. 查看服务器消息列表
3. 应该看到类似"BLE通知 [0000FFE4-...]: XX XX XX"的消息
```

## 🔧 高级功能

### 手动控制BLE连接

如果需要更精细的控制：
1. **连接BLE设备**：先点击"连接BLE设备"
2. **订阅特征值**：连接成功后点击"订阅0xFFE4"

### 多设备支持

统一服务器支持同时连接多个设备：
- 多个经典蓝牙设备可以同时连接
- 多个BLE设备可以同时连接
- 所有消息都会显示在同一个消息列表中

### 消息格式

接收到的消息格式：
- **经典蓝牙消息**：直接显示接收到的文本内容
- **BLE通知消息**：显示为"BLE通知 [特征值UUID]: 十六进制数据"

## 🛠️ 编程接口

### 启动统一服务器
```kotlin
val bluetoothManager = BluetoothManager.getInstance(context)
val success = bluetoothManager.startUnifiedBluetoothServer()
```

### 快速连接BLE设备
```kotlin
val success = bluetoothManager.autoConnectAndSubscribeBleDevice("AA:BB:CC:DD:EE:FF")
```

### 批量连接多个BLE设备
```kotlin
val devices = listOf("AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66")
bluetoothManager.autoConnectMultipleBleDevices(devices)
```

### 停止统一服务器
```kotlin
bluetoothManager.stopUnifiedBluetoothServer()
```

### 处理接收到的消息
```kotlin
bluetoothManager.addCallback(object : BluetoothCallback {
    override fun onDataReceived(senderAddress: String, data: String) {
        // 处理经典蓝牙消息
        Log.d("经典蓝牙", "收到消息: $data")
    }
    
    override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
        // 处理BLE特征值通知
        val dataHex = data.joinToString(" ") { "%02X".format(it) }
        Log.d("BLE通知", "收到通知: $dataHex")
    }
})
```

## 🔍 故障排除

### 问题1：BLE设备连接失败
**解决方案：**
- 确认MAC地址格式正确
- 确保设备在蓝牙范围内
- 在手机蓝牙设置中取消配对后重试

### 问题2：收不到BLE按键信号
**解决方案：**
- 确认使用的是统一服务器而不是经典蓝牙服务器
- 检查设备是否支持0xFFE4特征值
- 查看Android Studio Logcat中的调试日志

### 问题3：经典蓝牙设备无法连接
**解决方案：**
- 确保设备支持SPP协议
- 检查设备是否已配对
- 确认统一服务器正在运行

## 📊 性能特点

- **低延迟**：BLE通知和经典蓝牙消息都能实时接收
- **高可靠性**：自动重连和错误恢复机制
- **多设备支持**：可同时处理多个设备的消息
- **统一接口**：所有消息通过相同的回调接口处理

## 🎉 使用场景

### 场景1：智能家居控制
- BLE按键控制灯光开关
- 经典蓝牙设备发送传感器数据

### 场景2：工业自动化
- BLE按键触发设备操作
- 经典蓝牙设备传输状态信息

### 场景3：游戏控制
- BLE按键作为游戏手柄
- 经典蓝牙设备传输游戏数据

## 📝 注意事项

1. **权限要求**：需要蓝牙相关权限（BLUETOOTH_SCAN, BLUETOOTH_CONNECT等）
2. **Android版本**：建议Android 5.0+以获得最佳BLE支持
3. **设备兼容性**：不同厂商的BLE设备可能使用不同的特征值UUID
4. **电池优化**：长时间运行可能影响电池续航

## 🔄 版本更新

### v1.2.0 新功能
- ✅ 统一蓝牙服务器
- ✅ 自动BLE设备连接和订阅
- ✅ 增强的调试日志
- ✅ 快速连接按钮
- ✅ 多设备支持

现在您可以享受同时接收经典蓝牙和BLE设备消息的便利了！🎉
