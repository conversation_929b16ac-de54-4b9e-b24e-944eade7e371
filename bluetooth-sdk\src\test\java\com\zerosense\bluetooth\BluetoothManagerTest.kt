package com.zerosense.bluetooth

import android.content.Context
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.Mockito.*

@RunWith(MockitoJUnitRunner::class)
class BluetoothManagerTest {

    @Mock
    private lateinit var mockContext: Context

    @Test
    fun testBluetoothManagerInitialization() {
        // 模拟Context
        `when`(mockContext.applicationContext).thenReturn(mockContext)
        
        // 测试BluetoothManager初始化不会抛出异常
        try {
            val bluetoothManager = BluetoothManager.getInstance(mockContext)
            assert(bluetoothManager != null)
            println("BluetoothManager初始化成功")
        } catch (e: Exception) {
            println("BluetoothManager初始化失败: ${e.message}")
            throw e
        }
    }

    @Test
    fun testBleManagerLazyInitialization() {
        // 模拟Context
        `when`(mockContext.applicationContext).thenReturn(mockContext)
        
        // 测试BLE管理器延迟初始化
        try {
            val bluetoothManager = BluetoothManager.getInstance(mockContext)
            
            // BLE管理器应该在第一次调用BLE方法时才初始化
            // 这里只是测试不会在BluetoothManager初始化时就崩溃
            assert(bluetoothManager != null)
            println("BLE管理器延迟初始化测试通过")
        } catch (e: Exception) {
            println("BLE管理器延迟初始化测试失败: ${e.message}")
            throw e
        }
    }
}
