# 蓝牙连接问题排查指南

## 问题描述
连接蓝牙设备时出现错误："连接失败 read failed, socket might closed or timeout, read ret:-1"

## 解决方案

### 1. 改进的连接机制
我们已经实现了以下改进：

#### 多重连接方法
- **标准连接方法**: 使用 `createRfcommSocketToServiceRecord(SPP_UUID)`
- **反射连接方法**: 使用 `createRfcommSocket(1)` 通过反射调用
- **自动重试**: 连接失败时自动重试最多3次
- **已配对设备专用方法**: 对已配对设备使用更可靠的连接方式

#### 超时和重试机制
- **连接超时**: 未配对设备30秒，已配对设备20秒（给用户足够时间确认配对）
- **重试次数**: 减少到1次重试，避免过于激进的重试
- **重试间隔**: 失败后等待3秒再重试，给设备更多响应时间
- **扫描停止**: 连接前确保停止设备扫描
- **取消连接**: 用户可以在连接过程中取消操作
- **配对后连接**: 配对成功后自动等待3秒再建立连接，确保配对过程完全稳定

### 2. 使用方法

#### 连接未配对设备
```kotlin
bluetoothManager.connectToDevice(deviceAddress)
```

#### 连接已配对设备（推荐）
```kotlin
bluetoothManager.connectToPairedDevice(deviceAddress)
```

#### 静默连接已配对设备
```kotlin
bluetoothManager.connectToPairedDeviceAutomatically(deviceAddress)
```

#### 取消正在进行的连接
```kotlin
bluetoothManager.cancelConnection(deviceAddress)
```

#### 配对后连接（自动处理）
```kotlin
// 系统会自动监听配对状态变化
// 配对成功后会自动调用 connectAfterPairing() 方法
bluetoothManager.connectAfterPairing(deviceAddress)
```

## 新增功能

### 多种连接方法自动尝试
- **标准SPP方法**: 使用`createRfcommSocketToServiceRecord()`
- **反射方法(端口1-3)**: 使用`createRfcommSocket()`尝试不同端口
- **智能方法选择**: 根据设备配对状态和重试次数自动选择最佳方法
- **详细连接日志**: 显示每种连接方法的尝试结果

### 设备服务检测
- **自动检测**: 连接前检测设备支持的蓝牙服务
- **服务信息显示**: 显示设备支持的UUID服务列表
- **连接优化**: 根据服务信息优化连接策略

### 设备状态重置
- **缓存清理**: 重试前尝试清除设备连接缓存
- **状态重置**: 使用反射调用`refresh()`方法重置设备状态
- **智能重试**: 仅在配对设备首次连接失败时进行重置

### 3. 测试步骤

#### 步骤1: 确保权限正确
1. 检查应用是否已获得所有必要的蓝牙权限
2. 在Android 12+设备上，确保已授予 `BLUETOOTH_SCAN` 和 `BLUETOOTH_CONNECT` 权限

#### 步骤2: 测试设备配对
1. 先在系统设置中手动配对目标蓝牙设备
2. 在应用中刷新已配对设备列表
3. 使用"连接"按钮连接已配对设备

#### 步骤3: 测试连接和取消功能
1. 尝试连接未配对设备
2. 观察连接按钮变为"取消"按钮
3. 可以点击"取消"按钮中止连接
4. 对于配对请求，在设备上确认配对（有30秒时间）
5. 配对成功后会显示"配对成功！正在建立连接..."
6. 系统会自动等待5秒后尝试建立连接
7. 观察详细的连接过程信息：
   - 设备服务检测结果
   - 各种连接方法的尝试过程
   - Socket创建和连接时间
   - 重试和设备重置过程
8. 观察重试提示信息和详细错误信息

#### 步骤4: 测试不同连接方法
1. 对于已配对设备，使用"连接"按钮（会自动使用专用方法）
2. 对于未配对设备，使用普通连接方法
3. 尝试"静默连接"功能

### 4. 常见问题和解决方案

#### 问题1: 连接仍然失败
**解决方案**:
1. 确保目标设备支持SPP协议
2. 检查设备是否已被其他应用连接
3. 尝试重启蓝牙或重新配对设备

#### 问题2: 连接超时
**解决方案**:
1. 确保设备距离足够近（1-2米内）
2. 检查设备电量是否充足
3. 尝试清除蓝牙缓存（系统设置 > 应用 > 蓝牙 > 存储 > 清除缓存）

#### 问题3: 权限被拒绝
**解决方案**:
1. 在应用设置中手动授予所有蓝牙权限
2. 对于Android 12+，确保位置权限也已授予
3. 重启应用后重试

### 5. 调试信息

#### 查看连接状态
应用会显示详细的连接状态和错误信息：
- 连接中...
- 连接成功
- 连接失败（已重试X次）
- 连接超时

#### 日志信息
连接过程中的详细信息会通过回调接口返回，包括：
- 重试次数
- 使用的连接方法
- 具体的错误信息

### 6. 性能优化建议

1. **连接前停止扫描**: 确保在连接设备前停止蓝牙扫描
2. **避免并发连接**: 不要同时连接多个设备
3. **合理的重试间隔**: 给设备足够的时间响应
4. **及时释放资源**: 不使用时及时断开连接

### 7. 兼容性说明

这些改进已在以下情况下测试：
- Android 6.0+ 设备
- 各种蓝牙设备类型
- 已配对和未配对设备
- 不同的连接距离和环境

如果问题仍然存在，请提供以下信息：
1. 设备型号和Android版本
2. 目标蓝牙设备类型
3. 完整的错误日志
4. 连接时的具体步骤
