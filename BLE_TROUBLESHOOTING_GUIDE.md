# 蓝牙按键BLE连接故障排除指南

## 🔍 问题诊断

您的蓝牙按键没有收到信号，可能的原因和解决方案：

## 1. 确认使用正确的蓝牙协议

### ❌ 错误做法：
- 启动经典蓝牙服务器（SPP协议）
- 期望接收BLE设备的信号

### ✅ 正确做法：
- 使用BLE功能连接蓝牙按键
- 订阅0xFFE4特征值通知

## 2. 详细操作步骤

### 步骤1：获取蓝牙按键MAC地址
```
方法A：手机蓝牙设置
设置 → 蓝牙 → 找到按键设备 → 查看详情

方法B：Demo应用扫描
点击"开始BLE扫描" → 查看发现的设备
```

### 步骤2：在Demo应用中操作
1. **关闭经典蓝牙服务器**（如果正在运行）
2. **滚动到"BLE功能"部分**
3. **输入设备地址**：在"BLE设备地址"框中输入MAC地址
4. **连接设备**：点击"连接BLE设备"
5. **订阅通知**：点击"订阅0xFFE4"
6. **测试按键**：按下蓝牙按键，查看消息列表

## 3. 常见问题和解决方案

### 问题1：连接失败
**可能原因：**
- 设备地址错误
- 设备不在范围内
- 设备已被其他应用连接

**解决方案：**
- 确认MAC地址格式正确（AA:BB:CC:DD:EE:FF）
- 确保设备在蓝牙范围内（通常1-10米）
- 在手机蓝牙设置中断开其他连接

### 问题2：连接成功但订阅失败
**可能原因：**
- 设备不支持0xFFE4特征值
- 特征值不支持通知功能
- 权限不足

**解决方案：**
- 查看日志确认设备支持的特征值
- 确认特征值支持NOTIFY属性
- 检查蓝牙权限

### 问题3：订阅成功但收不到通知
**可能原因：**
- 按键没有正确发送数据
- 设备进入休眠模式
- 信号干扰

**解决方案：**
- 多次按键测试
- 重新连接设备
- 靠近设备测试

## 4. 调试日志查看

现在SDK已经增加了详细的调试日志，您可以通过以下方式查看：

### Android Studio Logcat
```
过滤标签：BleManager
查看关键日志：
- "开始订阅特征值"
- "找到目标特征值"
- "🔔 收到特征值通知!"
```

### 关键日志信息
```
正常流程应该看到：
1. "开始订阅特征值: [设备地址], UUID: [0xFFE4]"
2. "GATT连接存在，开始查找特征值"
3. "可用的服务和特征值:" (列出所有服务)
4. "找到目标特征值: [UUID]"
5. "本地通知启用成功"
6. "成功订阅特征值通知"
7. 按键时："🔔 收到特征值通知!"
```

## 5. 验证设备兼容性

### 检查设备是否支持0xFFE4
在日志中查看"可用的服务和特征值"部分，确认是否包含：
```
特征值: 0000ffe4-0000-1000-8000-00805f9b34fb
```

### 如果不支持0xFFE4
1. 查看设备文档确认正确的特征值UUID
2. 使用`subscribeToCharacteristic()`方法订阅正确的特征值

## 6. 完整测试代码示例

```kotlin
// 1. 连接BLE设备
bluetoothManager.connectToBleDevice("AA:BB:CC:DD:EE:FF")

// 2. 在连接成功回调中订阅
override fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {
    if (state == 2) { // 已连接
        bluetoothManager.subscribeToFFE4Notification(deviceAddress)
    }
}

// 3. 处理接收到的通知
override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
    val dataHex = data.joinToString(" ") { "%02X".format(it) }
    Log.d("按键", "收到按键信号: $dataHex")
}
```

## 7. 如果仍然无法解决

请提供以下信息：
1. 蓝牙按键的具体型号和品牌
2. Android Studio Logcat中的完整日志
3. 设备是否在手机蓝牙设置中显示为已连接
4. 是否有其他应用能够接收到按键信号

## 8. 常见蓝牙按键特征值

不同厂商的蓝牙按键可能使用不同的特征值：
- 0xFFE4: 常见的通用特征值
- 0xFFE1: 某些厂商使用
- 0x2A4D: HID按键特征值
- 自定义UUID: 某些厂商的专有特征值

如果0xFFE4不工作，请查看日志中列出的所有特征值，尝试其他可能的UUID。
