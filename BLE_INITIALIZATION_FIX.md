# BLE初始化问题修复说明

## 问题描述

在SDK初始化时出现 `Lazy.getValue() on a null object reference` 错误，导致SDK初始化失败。

## 问题原因

原因是在 `BluetoothManager` 的 `init` 块中过早地初始化了 `BleManager`：

```kotlin
// 问题代码
private val bleManager: BleManager by lazy { BleManager.getInstance(context) }

init {
    // 在init块中调用setupBleManagerCallbacks()会触发lazy初始化
    setupBleManagerCallbacks() // 这里会触发bleManager的初始化
}
```

当 `BluetoothManager` 正在构造时，`context` 可能还没有完全准备好，导致 `BleManager.getInstance(context)` 调用失败。

## 修复方案

采用**延迟初始化**策略，只有在真正需要使用BLE功能时才初始化 `BleManager`：

### 1. 修改BLE管理器声明

```kotlin
// 修复后的代码
private var bleManager: BleManager? = null
```

### 2. 添加获取BLE管理器的方法

```kotlin
private fun getBleManager(): BleManager {
    if (bleManager == null) {
        bleManager = BleManager.getInstance(context)
        setupBleManagerCallbacks()
    }
    return bleManager!!
}
```

### 3. 修改init块

```kotlin
init {
    Log.d(TAG, "BluetoothManager 开始初始化...")
    try {
        // 延迟启动连接池清理定时器，避免初始化时的潜在问题
        handler.postDelayed({
            startConnectionCleanupTimer()
        }, 1000) // 1秒后启动

        // 移除BLE管理器的初始化，改为延迟初始化
        // setupBleManagerCallbacks() // 删除这行

        Log.d(TAG, "BluetoothManager 初始化成功")
    } catch (e: Exception) {
        Log.e(TAG, "BluetoothManager 初始化失败: ${e.message}", e)
        throw e
    }
}
```

### 4. 更新所有BLE方法

```kotlin
fun startBleScan(): Boolean {
    return getBleManager().startBleScan() // 使用getBleManager()
}

fun stopBleScan() {
    getBleManager().stopBleScan() // 使用getBleManager()
}

// 其他BLE方法类似...
```

### 5. 更新资源清理

```kotlin
fun release() {
    stopScan()
    // 只有在BLE管理器已初始化时才清理
    bleManager?.let {
        it.stopBleScan()
        it.cleanup()
    }
    disconnectAll()
    callbacks.clear()
    INSTANCE = null
}
```

## 修复效果

1. **解决初始化崩溃**: `BluetoothManager` 初始化时不再会因为BLE管理器初始化失败而崩溃
2. **保持功能完整**: BLE功能在第一次调用时会自动初始化，功能不受影响
3. **提高性能**: 如果不使用BLE功能，BLE管理器不会被初始化，节省资源
4. **向后兼容**: 对现有的经典蓝牙功能没有任何影响

## 测试验证

1. SDK初始化不再抛出异常
2. BLE功能在第一次调用时正常工作
3. 经典蓝牙功能不受影响
4. 资源清理正常工作

## 使用建议

现在可以安全地初始化SDK：

```kotlin
// 初始化SDK - 不会再崩溃
ZeroSenseBluetoothSDK.initialize(context)
val bluetoothManager = ZeroSenseBluetoothSDK.getBluetoothManager()

// BLE功能会在第一次调用时自动初始化
bluetoothManager?.startBleScan() // 这里会触发BLE管理器的初始化
```
